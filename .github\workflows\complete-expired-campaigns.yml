name: Complete Expired Campaigns

on:
  schedule:
    - cron: "0 0 * * *" # Runs at midnight UTC every day
  workflow_dispatch: # Allows manual triggering

jobs:
  complete-campaigns:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: "20.12.0"
      - uses: pnpm/action-setup@v2
        with:
          version: 8
      - name: Install dependencies
        run: pnpm install
      - name: Run cron job
        run: cd packages/server && pnpm run cron:complete-campaigns
        env:
          DB_URL: ${{ secrets.DB_URL }}
          # Add any other required secrets here
