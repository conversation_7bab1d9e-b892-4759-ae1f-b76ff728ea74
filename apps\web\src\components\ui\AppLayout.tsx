"use client";

import { Fragment, ReactNode, useCallback, useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import { useUnreadMessages } from "@/hooks/use-unread-messages";
import { TokensStorage } from "@/lib/utils/tokens-storage";
import {
  Dialog,
  DialogPanel,
  Popover,
  PopoverButton,
  PopoverPanel,
  Transition,
} from "@headlessui/react";
import {
  Bars3Icon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import {
  ArrowLeftStartOnRectangleIcon,
  BuildingOfficeIcon,
  ChatBubbleLeftIcon,
  Cog6ToothIcon,
  CurrencyDollarIcon,
  DocumentIcon,
  HomeIcon,
  MegaphoneIcon,
  PencilIcon,
  UserGroupIcon,
  UserIcon,
} from "@heroicons/react/24/solid";

import { FullPageLoadingSpinner } from "./LoadingSpinner";

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  hasUnread?: boolean;
  unreadCount?: number;
}

interface AppLayoutProps {
  children: ReactNode;
  contentClassName?: string;
  footer?: ReactNode;
}

export function AppLayout({
  children,
  contentClassName = "tw-max-w-7xl",
  footer,
}: AppLayoutProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { getProfile, user, loading, signOut } = useAuth();
  const { hasUnreadMessages, unreadCount } = useUnreadMessages();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const checkAuth = useCallback(async () => {
    try {
      if (!user && TokensStorage.getTokens()) {
        const profile = await getProfile(true);
        if (!profile) {
          router.push("/auth/login");
        }
      } else if (!user) {
        router.push("/auth/login");
      }
      if (
        user?.userType === "brand" &&
        !user.brand?.subscriptionActive &&
        process.env.NEXT_PUBLIC_SKIP_SUBSCRIPTION !== "true"
      ) {
        router.push("/subscribe");
      }
    } catch (error) {
      console.error("Error checking auth:", error);
      router.push("/auth/login");
    }
  }, [getProfile, router, user]);

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  const handleSignOut = async () => {
    await signOut();
    router.push("/");
  };

  const navigation =
    user?.userType === "athlete"
      ? [
          { name: "Dashboard", href: "/app/athlete", icon: HomeIcon },
          { name: "Profile", href: `/app/athlete/${user?.id}`, icon: UserIcon },
          {
            name: "Chat",
            href: "/app/chat",
            icon: ChatBubbleLeftIcon,
            hasUnread: hasUnreadMessages,
            unreadCount,
          },
          {
            name: "Campaigns",
            href: "/app/athlete/campaigns",
            icon: MegaphoneIcon,
          },
          {
            name: "Contracts",
            href: "/app/athlete/contracts",
            icon: DocumentIcon,
          },
          {
            name: "Wallet",
            href: "/app/athlete/wallet",
            icon: CurrencyDollarIcon,
          },
        ]
      : [
          { name: "Dashboard", href: "/app/brand", icon: HomeIcon },
          {
            name: "Profile",
            href: `/app/brand/${user?.brand?._id}`,
            icon: BuildingOfficeIcon,
          },
          {
            name: "Athletes",
            href: "/app/brand/athletes",
            icon: UserGroupIcon,
          },
          {
            name: "Campaigns",
            href: "/app/brand/campaigns",
            icon: MegaphoneIcon,
          },
          {
            name: "Chat",
            href: "/app/chat",
            icon: ChatBubbleLeftIcon,
            hasUnread: hasUnreadMessages,
            unreadCount,
          },
          {
            name: "Contracts",
            href: "/app/brand/contracts",
            icon: DocumentIcon,
          },
        ];

  const profileMenuItems =
    user?.userType === "athlete"
      ? [
          {
            name: "Edit Profile",
            href: "/app/athlete/profile/edit",
            icon: PencilIcon,
          },
          {
            name: "Settings",
            href: "/app/athlete/settings",
            icon: Cog6ToothIcon,
          },
          {
            name: "Sign out",
            icon: ArrowLeftStartOnRectangleIcon,
            onClick: handleSignOut,
          },
        ]
      : [
          {
            name: "Edit Profile",
            href: "/app/brand/profile",
            icon: PencilIcon,
          },
          {
            name: "Settings",
            href: "/app/brand/settings",
            icon: Cog6ToothIcon,
          },
          {
            name: "Sign out",
            icon: ArrowLeftStartOnRectangleIcon,
            onClick: handleSignOut,
          },
        ];

  const profileImage =
    user?.athlete?.profilePicture?.url || user?.brand?.logo?.url;
  const profileName = user?.name || user?.brand?.companyName;

  const NavItem = ({ item }: { item: NavigationItem }) => {
    const isActive = pathname === item.href;
    const Icon = item.icon;
    return (
      <Link
        href={item.href}
        className={`tw-flex tw-items-center tw-px-3 tw-py-2 tw-text-sm tw-relative ${
          isActive
            ? "tw-text-aims-primary tw-font-medium"
            : "tw-text-aims-text-secondary hover:tw-text-aims-text-primary"
        }`}
      >
        <Icon
          className={`tw-h-5 tw-w-5 tw-mr-2 ${
            isActive ? "tw-text-aims-primary" : "tw-text-aims-text-secondary"
          }`}
        />
        <span className="tw-relative">
          {item.name}
          {item.hasUnread && (
            <div
              className={`tw-absolute -tw-top-2 -tw-right-3.5 tw-w-4 tw-h-4 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-border-2 tw-border-aims-dark-2 ${
                isActive ? "tw-bg-aims-primary" : "tw-bg-aims-text-secondary"
              }`}
            >
              <span className="tw-text-xs tw-text-black tw-font-medium tw-text-center tw-leading-none">
                {item.unreadCount && item.unreadCount > 9
                  ? "9+"
                  : item.unreadCount}
              </span>
            </div>
          )}
        </span>
      </Link>
    );
  };

  if (loading) {
    return <FullPageLoadingSpinner />;
  }

  return (
    <div className="tw-min-h-screen tw-flex tw-flex-col">
      {/* Top Navigation */}
      <nav className="tw-fixed tw-top-0 tw-left-0 tw-right-0 tw-z-40 tw-bg-aims-dark-2">
        <div className="tw-flex tw-justify-between tw-h-16">
          {/* Logo - Left aligned */}
          <div className="tw-flex tw-items-center tw-px-4">
            <Link href="/" className="tw-flex tw-items-center">
              <span className="tw-text-xl tw-font-semibold tw-text-aims-primary">
                AIMS
              </span>
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="tw-flex tw-items-center tw-px-4 md:tw-hidden">
            <button
              type="button"
              className="tw-text-aims-text-secondary hover:tw-text-aims-text-primary"
              onClick={() => setMobileMenuOpen(true)}
            >
              <Bars3Icon className="tw-h-6 tw-w-6" />
            </button>
          </div>

          {/* Navigation Links - Centered (hidden on mobile) */}
          <div className="tw-hidden md:tw-flex tw-items-center tw-justify-center tw-flex-1">
            <div className="tw-flex tw-items-center tw-space-x-4">
              {(navigation.filter(Boolean) as NavigationItem[]).map((item) => (
                <NavItem key={item.name} item={item} />
              ))}
            </div>
          </div>

          {/* User section - Right aligned (hidden on mobile) */}
          <div className="tw-hidden md:tw-flex tw-items-center tw-px-4">
            <div className="tw-flex tw-items-center tw-space-x-4 tw-gap-x-8">
              {/* Notifications */}
              {/* <button className="tw-text-aims-text-secondary hover:tw-text-aims-text-primary">
                <BellIcon className="tw-h-6 tw-w-6" />
              </button> */}

              {/* Profile dropdown */}
              <Popover className="tw-relative">
                <PopoverButton className="tw-flex tw-items-center tw-space-x-3">
                  <div className="tw-relative tw-h-8 tw-w-8 tw-rounded-full tw-overflow-hidden">
                    <Image
                      src={profileImage || "/no-profile-pic.jpg"}
                      alt={`${profileName || "User"}'s profile`}
                      fill
                      sizes="128px"
                      className="tw-object-cover"
                      quality={100}
                      priority
                    />
                  </div>
                  <div className="tw-flex tw-items-center">
                    <span className="tw-text-sm tw-font-medium tw-text-aims-text-secondary">
                      {profileName}
                    </span>
                  </div>
                </PopoverButton>

                <Transition
                  as={Fragment}
                  enter="tw-transition tw-ease-out tw-duration-100"
                  enterFrom="tw-transform tw-opacity-0 tw-scale-95"
                  enterTo="tw-transform tw-opacity-100 tw-scale-100"
                  leave="tw-transition tw-ease-in tw-duration-75"
                  leaveFrom="tw-transform tw-opacity-100 tw-scale-100"
                  leaveTo="tw-transform tw-opacity-0 tw-scale-95"
                >
                  <PopoverPanel className="tw-absolute tw-right-0 tw-mt-2 tw-w-48 tw-origin-top-right tw-rounded-md tw-bg-aims-dark-2 tw-py-1 tw-shadow-lg tw-ring-1 tw-ring-black tw-ring-opacity-5 focus:tw-outline-none">
                    {profileMenuItems.map((item) =>
                      item.href ? (
                        <Link
                          key={item.name}
                          href={item.href}
                          className="tw-group tw-flex tw-w-full tw-items-center tw-px-4 tw-py-2 tw-text-sm tw-text-aims-text-secondary hover:tw-text-aims-text-primary hover:tw-bg-aims-dark-3"
                        >
                          <item.icon className="tw-mr-3 tw-h-5 tw-w-5" />
                          {item.name}
                        </Link>
                      ) : (
                        <button
                          key={item.name}
                          onClick={item.onClick}
                          className="tw-group tw-flex tw-w-full tw-items-center tw-px-4 tw-py-2 tw-text-sm tw-text-aims-text-secondary hover:tw-text-aims-text-primary hover:tw-bg-aims-dark-3"
                        >
                          <item.icon className="tw-mr-3 tw-h-5 tw-w-5" />
                          {item.name}
                        </button>
                      ),
                    )}
                  </PopoverPanel>
                </Transition>
              </Popover>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile menu */}
      <Dialog
        as="div"
        className="md:tw-hidden"
        open={mobileMenuOpen}
        onClose={setMobileMenuOpen}
      >
        <div className="tw-fixed tw-inset-0 tw-z-50" />
        <DialogPanel className="tw-fixed tw-inset-y-0 tw-right-0 tw-z-50 tw-w-full tw-overflow-y-auto tw-bg-aims-dark-2 tw-px-6 tw-py-6 tw-sm:tw-max-w-sm tw-sm:tw-ring-1 tw-sm:tw-ring-gray-900/10">
          <div className="tw-flex tw-items-center tw-justify-between">
            <Link href="/" className="tw-flex tw-items-center">
              <span className="tw-text-xl tw-font-semibold tw-text-aims-primary">
                AIMS
              </span>
            </Link>
            <button
              type="button"
              className="tw-text-aims-text-secondary hover:tw-text-aims-text-primary"
              onClick={() => setMobileMenuOpen(false)}
            >
              <XMarkIcon className="tw-h-6 tw-w-6" />
            </button>
          </div>
          <div className="tw-mt-6 tw-flow-root">
            <div className="tw-space-y-2 tw-py-6">
              {(navigation.filter(Boolean) as NavigationItem[]).map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`tw-flex tw-items-center tw-px-3 tw-py-2 tw-text-base tw-relative ${
                    pathname === item.href
                      ? "tw-text-aims-primary tw-font-medium"
                      : "tw-text-aims-text-secondary hover:tw-text-aims-text-primary"
                  }`}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <item.icon
                    className={`tw-h-6 tw-w-6 tw-mr-3 ${
                      pathname === item.href
                        ? "tw-text-aims-primary"
                        : "tw-text-aims-text-secondary"
                    }`}
                  />
                  <span className="tw-relative">
                    {item.name}
                    {item.hasUnread && (
                      <div
                        className={`tw-absolute -tw-top-2 -tw-right-3.5 tw-w-4 tw-h-4 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-border-2 tw-border-aims-dark-2 ${
                          pathname === item.href
                            ? "tw-bg-aims-primary"
                            : "tw-bg-aims-text-secondary"
                        }`}
                      >
                        <span className="tw-text-xs tw-text-black tw-font-medium tw-text-center tw-leading-none">
                          {item.unreadCount && item.unreadCount > 9
                            ? "9+"
                            : item.unreadCount}
                        </span>
                      </div>
                    )}
                  </span>
                </Link>
              ))}
            </div>
            <div className="tw-py-6">
              {/* <div className="tw-flex tw-items-center tw-space-x-3 tw-px-3">
                <div className="tw-relative tw-h-10 tw-w-10 tw-rounded-full tw-overflow-hidden">
                  <Image
                    src={profileImage || "/no-profile-pic.jpg"}
                    alt={`${profileName || "User"}'s profile`}
                    fill
                    sizes="40px"
                    className="tw-object-cover"
                    quality={100}
                    priority
                  />
                </div>
                <div className="tw-flex tw-flex-col">
                  <span className="tw-text-sm tw-font-medium tw-text-aims-text-secondary">
                    {profileName}
                  </span>
                </div>
              </div> */}
              <div className="tw-mt-6 tw-space-y-1">
                {profileMenuItems.map((item) =>
                  item.href ? (
                    <Link
                      key={item.name}
                      href={item.href}
                      className="tw-flex tw-items-center tw-px-3 tw-py-2 tw-text-base tw-text-aims-text-secondary hover:tw-text-aims-text-primary"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <item.icon className="tw-mr-3 tw-h-6 tw-w-6" />
                      {item.name}
                    </Link>
                  ) : (
                    <button
                      key={item.name}
                      onClick={() => {
                        item.onClick?.();
                        setMobileMenuOpen(false);
                      }}
                      className="tw-flex tw-w-full tw-items-center tw-px-3 tw-py-2 tw-text-base tw-text-aims-text-secondary hover:tw-text-aims-text-primary"
                    >
                      <item.icon className="tw-mr-3 tw-h-6 tw-w-6" />
                      {item.name}
                    </button>
                  ),
                )}
              </div>
            </div>
          </div>
        </DialogPanel>
      </Dialog>

      {/* Main content */}
      <main className="tw-flex-1 tw-pt-16">
        <div className={`${contentClassName}`}>{children}</div>
      </main>
      {footer && footer}
    </div>
  );
}
